import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../services/firebase_service.dart';
import '../models/game_room.dart';
import '../widgets/banner_ad_widget.dart';

import '../l10n/app_localizations.dart';
import 'game_screen.dart';

class WaitingRoomScreen extends StatefulWidget {
  final String roomId;
  final String playerId;

  const WaitingRoomScreen({
    super.key,
    required this.roomId,
    required this.playerId,
  });

  @override
  State<WaitingRoomScreen> createState() => _WaitingRoomScreenState();
}

class _WaitingRoomScreenState extends State<WaitingRoomScreen> {
  GameRoom? _lastRoom;

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    final firebaseService = Provider.of<FirebaseService>(context, listen: false);
    
    return Scaffold(
      appBar: AppBar(
        title: Text(l10n.waitingForPlayers),
        backgroundColor: Colors.transparent,
        elevation: 0,
        foregroundColor: Colors.white,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            Navigator.popUntil(context, (route) => route.isFirst);
          },
        ),
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF0F0F23),
              Color(0xFF1A1A2E),
              Color(0xFF16213E),
            ],
          ),
        ),
        child: StreamBuilder<GameRoom?>(
          stream: firebaseService.watchRoom(widget.roomId),
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return const Center(
                child: CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF0E4B99)),
                ),
              );
            }

            if (snapshot.hasError) {
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(
                      Icons.error_outline,
                      size: 64,
                      color: Colors.red,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      AppLocalizations.of(context)!.connectionError(snapshot.error.toString()),
                      style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                        color: Colors.white,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: () {
                        Navigator.popUntil(context, (route) => route.isFirst);
                      },
                      child: Text(AppLocalizations.of(context)!.goBack),
                    ),
                  ],
                ),
              );
            }

            if (!snapshot.hasData || snapshot.data == null) {
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(
                      Icons.search_off,
                      size: 64,
                      color: Colors.orange,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      AppLocalizations.of(context)!.roomNotFound(widget.roomId),
                      style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                        color: Colors.white,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: () {
                        Navigator.popUntil(context, (route) => route.isFirst);
                      },
                      child: Text(AppLocalizations.of(context)!.goBack),
                    ),
                  ],
                ),
              );
            }

            final room = snapshot.data!;

            // Force UI update
            print('🔄 StreamBuilder rebuilding UI with new room data');
            WidgetsBinding.instance.addPostFrameCallback((_) {
              if (mounted) {
                setState(() {
                  _lastRoom = room;
                });
              }
            });

            final currentPlayer = room.players.firstWhere(
              (p) => p.id == widget.playerId,
              orElse: () => room.players.first,
            );
            final isHost = currentPlayer.isHost;

            // Debug: Print all players' ready status
            print('👥 Players ready status:');
            for (final player in room.players) {
              print('   ${player.name}: ${player.isReady ? "READY" : "NOT READY"}');
            }
            print('🎮 Can start game: ${room.canStartGame}');

            // Navigate to game screen if game started
            if (room.state == GameState.playing) {
              WidgetsBinding.instance.addPostFrameCallback((_) {
                if (mounted) {
                  Navigator.pushReplacement(
                    context,
                    MaterialPageRoute(
                      builder: (context) => GameScreen(
                        roomId: widget.roomId,
                        playerId: widget.playerId,
                      ),
                    ),
                  );
                }
              });
            }

            return SafeArea(
              child: Padding(
                padding: const EdgeInsets.all(24.0),
                child: Column(
                  children: [
                    // Room Code Card
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(24),
                      decoration: BoxDecoration(
                        color: const Color(0xFF16213E),
                        borderRadius: BorderRadius.circular(16),
                        border: Border.all(
                          color: const Color(0xFF0E4B99),
                          width: 2,
                        ),
                      ),
                      child: Column(
                        children: [
                          Text(
                            l10n.roomCode,
                            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                              color: Colors.white60,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text(
                                room.code,
                                style: Theme.of(context).textTheme.headlineLarge?.copyWith(
                                  color: const Color(0xFF0E4B99),
                                  fontWeight: FontWeight.bold,
                                  letterSpacing: 4,
                                ),
                              ),
                              const SizedBox(width: 16),
                              IconButton(
                                onPressed: () {
                                  Clipboard.setData(ClipboardData(text: room.code));
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    SnackBar(
                                      content: Text(AppLocalizations.of(context)!.roomCodeCopied),
                                      duration: const Duration(seconds: 2),
                                    ),
                                  );
                                },
                                icon: const Icon(
                                  Icons.copy,
                                  color: Color(0xFF0E4B99),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                    
                    const SizedBox(height: 24),
                    
                    // Players List
                    Expanded(
                      child: Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(20),
                        decoration: BoxDecoration(
                          color: const Color(0xFF16213E).withOpacity(0.5),
                          borderRadius: BorderRadius.circular(16),
                          border: Border.all(
                            color: const Color(0xFF0E4B99).withOpacity(0.3),
                          ),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                const Icon(
                                  Icons.people,
                                  color: Color(0xFF0E4B99),
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  '${l10n.players} (${room.players.length}/12)',
                                  style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                                    color: Colors.white,
                                    fontSize: 20,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 16),
                            Expanded(
                              child: ListView.builder(
                                itemCount: room.players.length,
                                itemBuilder: (context, index) {
                                  final player = room.players[index];
                                  return Container(
                                    margin: const EdgeInsets.only(bottom: 8),
                                    padding: const EdgeInsets.all(16),
                                    decoration: BoxDecoration(
                                      color: player.id == widget.playerId
                                          ? const Color(0xFF0E4B99).withOpacity(0.3)
                                          : const Color(0xFF16213E),
                                      borderRadius: BorderRadius.circular(12),
                                      border: Border.all(
                                        color: player.id == widget.playerId
                                            ? const Color(0xFF0E4B99)
                                            : Colors.transparent,
                                      ),
                                    ),
                                    child: Row(
                                      children: [
                                        CircleAvatar(
                                          backgroundColor: const Color(0xFF0E4B99),
                                          child: Text(
                                            player.name[0].toUpperCase(),
                                            style: const TextStyle(
                                              color: Colors.white,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                        ),
                                        const SizedBox(width: 12),
                                        Expanded(
                                          child: Column(
                                            crossAxisAlignment: CrossAxisAlignment.start,
                                            children: [
                                              Text(
                                                player.name,
                                                style: const TextStyle(
                                                  color: Colors.white,
                                                  fontWeight: FontWeight.w600,
                                                ),
                                              ),
                                              Row(
                                                children: [
                                                  if (player.isHost)
                                                    Text(
                                                      AppLocalizations.of(context)!.host,
                                                      style: const TextStyle(
                                                        color: Color(0xFF0E4B99),
                                                        fontSize: 12,
                                                      ),
                                                    ),
                                                  if (player.isHost && player.isReady)
                                                    const SizedBox(width: 8),
                                                  if (player.isReady)
                                                    Container(
                                                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                                                      decoration: BoxDecoration(
                                                        color: Colors.green,
                                                        borderRadius: BorderRadius.circular(8),
                                                      ),
                                                      child: const Text(
                                                        'HAZIR',
                                                        style: TextStyle(
                                                          color: Colors.white,
                                                          fontSize: 10,
                                                          fontWeight: FontWeight.bold,
                                                        ),
                                                      ),
                                                    ),
                                                ],
                                              ),
                                            ],
                                          ),
                                        ),
                                        Icon(
                                          player.isOnline ? Icons.circle : Icons.circle_outlined,
                                          color: player.isOnline ? Colors.green : Colors.grey,
                                          size: 12,
                                        ),
                                      ],
                                    ),
                                  );
                                },
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    
                    const SizedBox(height: 24),

                    // Ready Button (for all players)
                    Builder(
                      builder: (context) {
                        print('🔘 Ready button - Current player: ${currentPlayer.name}, isReady: ${currentPlayer.isReady}');
                        return SizedBox(
                          width: double.infinity,
                          child: ElevatedButton(
                            onPressed: () => _toggleReady(firebaseService),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: currentPlayer.isReady
                                  ? Colors.green
                                  : const Color(0xFF0E4B99),
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(vertical: 16),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                            ),
                            child: Text(
                              currentPlayer.isReady ? 'HAZIR DEĞİL' : 'HAZIR',
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        );
                      },
                    ),

                    const SizedBox(height: 16),

                    // Start Game Button (only for host)
                    if (isHost) ...[
                      SizedBox(
                        width: double.infinity,
                        child: ElevatedButton(
                          onPressed: room.canStartGame
                              ? () => _startGame(firebaseService)
                              : null,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: const Color(0xFF0E4B99),
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 20),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(16),
                            ),
                          ),
                          child: Text(
                            l10n.startGame,
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(height: 8),
                      if (!room.canStartGame)
                        Text(
                          room.players.length < 4
                              ? AppLocalizations.of(context)!.needMinPlayers
                              : 'Tüm oyuncuların hazır olmasını bekleyin',
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Colors.white60,
                          ),
                          textAlign: TextAlign.center,
                        ),
                    ] else ...[
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: const Color(0xFF16213E).withOpacity(0.5),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          l10n.waitingForPlayers,
                          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                            color: Colors.white70,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ],

                    // Banner Ad at the bottom
                    const Padding(
                      padding: EdgeInsets.only(top: 16),
                      child: BannerAdWidget(),
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Future<void> _startGame(FirebaseService firebaseService) async {
    try {
      await firebaseService.startGame(widget.roomId);
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(AppLocalizations.of(context)!.failedToStartGame(e.toString())),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _toggleReady(FirebaseService firebaseService) async {
    print('🎯 Ready button pressed - roomId: ${widget.roomId}, playerId: ${widget.playerId}');
    try {
      await firebaseService.togglePlayerReady(widget.roomId, widget.playerId);
      print('✅ Toggle ready completed successfully');
    } catch (e) {
      print('❌ Error in _toggleReady: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Hazır durumu değiştirilemedi: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
