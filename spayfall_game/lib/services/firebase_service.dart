import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_database/firebase_database.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:uuid/uuid.dart';
import '../models/game_room.dart';
import '../models/player.dart';

import '../data/locations.dart';
import '../firebase_options.dart';
import 'analytics_service.dart';
import 'dart:math';
import 'dart:convert';

class FirebaseService {
  static final FirebaseService _instance = FirebaseService._internal();
  factory FirebaseService() => _instance;
  FirebaseService._internal();

  late FirebaseDatabase _database;
  late FirebaseAnalytics _analytics;
  final Uuid _uuid = const Uuid();
  bool _isInitialized = false;

  Future<void> initialize() async {
    if (_isInitialized) {
      return; // Already initialized
    }

    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    );
    _database = FirebaseDatabase.instance;
    _analytics = FirebaseAnalytics.instance;
    _isInitialized = true;

    // Initialize AnalyticsService
    AnalyticsService().initialize(_analytics);
  }

  // Getters
  FirebaseAnalytics get analytics => _analytics;
  FirebaseDatabase get database => _database;

  // Room Management
  Future<GameRoom> createRoom(String hostName) async {
    final roomCode = _generateRoomCode();
    final hostId = _uuid.v4();
    final roomId = _uuid.v4();

    final host = Player(
      id: hostId,
      name: hostName,
      isHost: true,
    );

    final room = GameRoom(
      id: roomId,
      code: roomCode,
      hostId: hostId,
      players: [host],
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    await _database.ref('rooms/$roomId').set(room.toJson());

    // Analytics
    await AnalyticsService().logGameCreated(
      roomCode: roomCode,
      playerCount: room.players.length,
    );

    return room;
  }

  Future<GameRoom?> joinRoom(String roomCode, String playerName) async {
    try {
      final roomSnapshot = await _database.ref('rooms')
          .orderByChild('code')
          .equalTo(roomCode)
          .once();

      if (roomSnapshot.snapshot.value == null) {
        throw Exception('Room not found');
      }

      final roomData = roomSnapshot.snapshot.value as Map<dynamic, dynamic>;
      final roomId = roomData.keys.first;
      final rawRoomData = roomData[roomId];

      // Firebase'den gelen veriyi JSON string'e çevirip tekrar parse et
      final jsonString = jsonEncode(rawRoomData);
      final roomJson = jsonDecode(jsonString) as Map<String, dynamic>;
      final room = GameRoom.fromJson(roomJson);

      if (room.state != GameState.waiting) {
        throw Exception('Game already started');
      }

      if (room.players.length >= 12) {
        throw Exception('Room is full');
      }

      final playerId = _uuid.v4();
      final newPlayer = Player(
        id: playerId,
        name: playerName,
      );

      final updatedPlayers = [...room.players, newPlayer];
      final updatedRoom = room.copyWith(
        players: updatedPlayers,
        updatedAt: DateTime.now(),
      );

      await _database.ref('rooms/$roomId').update(updatedRoom.toJson());

      // Analytics
      await AnalyticsService().logGameJoined(
        roomCode: roomCode,
        playerCount: updatedRoom.players.length,
      );

      return updatedRoom;
    } catch (e) {
      print('❌ Error in joinRoom: $e');
      rethrow;
    }
  }

  Future<void> startGame(String roomId) async {
    final roomRef = _database.ref('rooms/$roomId');
    final roomSnapshot = await roomRef.once();
    
    if (roomSnapshot.snapshot.value == null) {
      throw Exception('Room not found');
    }

    // Firebase'den gelen veriyi JSON string'e çevirip tekrar parse et
    final jsonString = jsonEncode(roomSnapshot.snapshot.value);
    final roomJson = jsonDecode(jsonString) as Map<String, dynamic>;
    final room = GameRoom.fromJson(roomJson);

    if (room.players.length < 4) {
      throw Exception('Need at least 4 players to start');
    }

    // Assign roles
    final location = LocationsData.getRandomLocation();
    final shuffledPlayers = List<Player>.from(room.players);
    shuffledPlayers.shuffle();

    // First player becomes spy
    final spyPlayer = shuffledPlayers[0].copyWith(role: PlayerRole.spy);
    
    // Rest become civilians with specific roles
    final updatedPlayers = <Player>[spyPlayer];
    final roles = location.getRoles('en'); // Default to English for now
    
    for (int i = 1; i < shuffledPlayers.length; i++) {
      final roleIndex = (i - 1) % roles.length;
      final civilianPlayer = shuffledPlayers[i].copyWith(
        role: PlayerRole.civilian,
        specificRole: roles[roleIndex],
      );
      updatedPlayers.add(civilianPlayer);
    }

    final now = DateTime.now();
    final updatedRoom = room.copyWith(
      players: updatedPlayers,
      state: GameState.playing,
      currentLocation: location,
      spySeenLocations: [location.id], // Casusun gördüğü ilk mekan
      roundStartTime: now,
      roundEndTime: now.add(Duration(minutes: room.roundDurationMinutes)),
      updatedAt: now,
    );

    await roomRef.update(updatedRoom.toJson());

    // Analytics
    await AnalyticsService().logGameStarted(
      roomCode: room.code,
      playerCount: updatedRoom.players.length,
      location: location.id,
    );
  }

  Future<void> togglePlayerReady(String roomId, String playerId) async {
    print('🔄 togglePlayerReady called - roomId: $roomId, playerId: $playerId');

    final roomRef = _database.ref('rooms/$roomId');
    final roomSnapshot = await roomRef.once();

    if (roomSnapshot.snapshot.value == null) {
      print('❌ Room not found');
      throw Exception('Room not found');
    }

    // Firebase'den gelen veriyi JSON string'e çevirip tekrar parse et
    final jsonString = jsonEncode(roomSnapshot.snapshot.value);
    final roomJson = jsonDecode(jsonString) as Map<String, dynamic>;
    final room = GameRoom.fromJson(roomJson);

    print('📊 Current room state: ${room.state}');
    if (room.state != GameState.waiting) {
      print('❌ Game is not in waiting state');
      throw Exception('Game is not in waiting state');
    }

    final currentPlayer = room.players.firstWhere((p) => p.id == playerId, orElse: () => throw Exception('Player not found'));
    print('👤 Current player ready status: ${currentPlayer.isReady}');

    final updatedPlayers = room.players.map((player) {
      if (player.id == playerId) {
        final newReadyStatus = !player.isReady;
        print('✅ Toggling player ready status to: $newReadyStatus');
        return player.copyWith(isReady: newReadyStatus);
      }
      return player;
    }).toList();

    final updatedRoom = room.copyWith(
      players: updatedPlayers,
      updatedAt: DateTime.now(),
    );

    print('💾 Updating room in Firebase...');
    await roomRef.update(updatedRoom.toJson());
    print('✅ Room updated successfully');
  }

  Future<void> accusePlayer(String roomId, String accuserId, String accusedId) async {
    final roomRef = _database.ref('rooms/$roomId');
    final roomSnapshot = await roomRef.once();
    
    if (roomSnapshot.snapshot.value == null) {
      throw Exception('Room not found');
    }

    // Firebase'den gelen veriyi JSON string'e çevirip tekrar parse et
    final jsonString = jsonEncode(roomSnapshot.snapshot.value);
    final roomJson = jsonDecode(jsonString) as Map<String, dynamic>;
    final room = GameRoom.fromJson(roomJson);

    if (room.state != GameState.playing) {
      throw Exception('Game is not in playing state');
    }

    final accuser = room.players.firstWhere((p) => p.id == accuserId);
    if (accuser.hasAccused) {
      throw Exception('Player has already made an accusation this round');
    }

    final accused = room.players.firstWhere((p) => p.id == accusedId);

    // Analytics
    await AnalyticsService().logPlayerAccused(
      roomCode: room.code,
      accuserRole: accuser.role?.toString() ?? 'unknown',
      accusedRole: accused.role?.toString() ?? 'unknown',
      roundNumber: room.currentRound,
    );

    final updatedRoom = room.copyWith(
      state: GameState.voting,
      currentAccuserId: accuserId,
      accusedPlayerId: accusedId,
      votes: {},
      updatedAt: DateTime.now(),
    );

    await roomRef.update(updatedRoom.toJson());
  }

  Future<void> vote(String roomId, String playerId, bool voteYes) async {
    final roomRef = _database.ref('rooms/$roomId');
    final roomSnapshot = await roomRef.once();
    
    if (roomSnapshot.snapshot.value == null) {
      throw Exception('Room not found');
    }

    // Firebase'den gelen veriyi JSON string'e çevirip tekrar parse et
    final jsonString = jsonEncode(roomSnapshot.snapshot.value);
    final roomJson = jsonDecode(jsonString) as Map<String, dynamic>;
    final room = GameRoom.fromJson(roomJson);

    if (room.state != GameState.voting) {
      throw Exception('Not in voting phase');
    }

    if (playerId == room.accusedPlayerId) {
      throw Exception('Accused player cannot vote');
    }

    final updatedVotes = Map<String, bool>.from(room.votes);
    updatedVotes[playerId] = voteYes;

    // Analytics
    final voter = room.players.firstWhere((p) => p.id == playerId);
    await AnalyticsService().logVoteCast(
      roomCode: room.code,
      voterRole: voter.role?.toString() ?? 'unknown',
      voteYes: voteYes,
      roundNumber: room.currentRound,
    );

    // Check if all eligible players have voted
    final eligibleVoters = room.players
        .where((p) => p.id != room.accusedPlayerId)
        .map((p) => p.id)
        .toList();

    if (updatedVotes.length == eligibleVoters.length) {
      // All votes are in, process result
      await _processVoteResult(roomId, room, updatedVotes);
    } else {
      // Update votes and wait for more
      final updatedRoom = room.copyWith(
        votes: updatedVotes,
        updatedAt: DateTime.now(),
      );
      await roomRef.update(updatedRoom.toJson());
    }
  }

  Future<void> guessLocation(String roomId, String playerId, String locationId) async {
    print('🎯 Spy guessing location: $locationId');

    final roomRef = _database.ref('rooms/$roomId');
    final roomSnapshot = await roomRef.once();

    if (roomSnapshot.snapshot.value == null) {
      throw Exception('Room not found');
    }

    // Firebase'den gelen veriyi JSON string'e çevirip tekrar parse et
    final jsonString = jsonEncode(roomSnapshot.snapshot.value);
    final roomJson = jsonDecode(jsonString) as Map<String, dynamic>;
    final room = GameRoom.fromJson(roomJson);

    print('🏠 Current location: ${room.currentLocation?.id}');
    print('🎯 Guessed location: $locationId');

    final player = room.players.firstWhere((p) => p.id == playerId);
    if (player.role != PlayerRole.spy) {
      throw Exception('Only spy can guess location');
    }

    // Casusun gördüğü mekanları güncelle
    final updatedSpySeenLocations = List<String>.from(room.spySeenLocations);
    if (!updatedSpySeenLocations.contains(locationId)) {
      updatedSpySeenLocations.add(locationId);
    }

    final isCorrect = room.currentLocation?.id == locationId;
    print('✅ Is guess correct: $isCorrect');

    // Analytics
    await AnalyticsService().logLocationGuessed(
      roomCode: room.code,
      guessedLocation: locationId,
      actualLocation: room.currentLocation?.id ?? 'unknown',
      correct: isCorrect,
      roundNumber: room.currentRound,
    );

    if (isCorrect) {
      print('🎉 Spy wins - correct guess!');
      // Spy wins
      await _endRound(roomId, room.copyWith(spySeenLocations: updatedSpySeenLocations), spyWins: true, reason: 'Spy guessed location correctly');
    } else {
      print('❌ Civilians win - wrong guess!');
      // Civilians win
      await _endRound(roomId, room.copyWith(spySeenLocations: updatedSpySeenLocations), spyWins: false, reason: 'Spy guessed location incorrectly');
    }
  }

  Future<void> _processVoteResult(String roomId, GameRoom room, Map<String, bool> votes) async {
    final yesVotes = votes.values.where((v) => v).length;
    final totalVotes = votes.length;
    
    // Check for unanimous vote
    if (yesVotes == totalVotes) {
      // Unanimous yes vote
      final accusedPlayer = room.players.firstWhere((p) => p.id == room.accusedPlayerId);
      
      if (accusedPlayer.role == PlayerRole.spy) {
        // Correctly accused spy - civilians win
        await _endRound(roomId, room, spyWins: false, reason: 'Spy was correctly accused');
      } else {
        // Wrongly accused civilian - spy wins
        await _endRound(roomId, room, spyWins: true, reason: 'Civilian was wrongly accused');
      }
    } else {
      // Not unanimous - continue game
      final updatedRoom = room.copyWith(
        state: GameState.playing,
        currentAccuserId: null,
        accusedPlayerId: null,
        votes: {},
        updatedAt: DateTime.now(),
      );
      
      await _database.ref('rooms/$roomId').update(updatedRoom.toJson());
    }
  }

  Future<void> _endRound(String roomId, GameRoom room, {required bool spyWins, required String reason}) async {
    print('🏁 Ending round: $reason');
    print('🏆 Spy wins: $spyWins');

    // Calculate scores
    final updatedPlayers = room.players.map((player) {
      int additionalScore = 0;

      if (spyWins && player.role == PlayerRole.spy) {
        additionalScore = 4; // Spy wins
      } else if (!spyWins && player.role == PlayerRole.civilian) {
        additionalScore = 1; // Civilian wins
        if (player.id == room.currentAccuserId) {
          additionalScore = 2; // Bonus for successful accuser
        }
      }

      return player.copyWith(
        score: player.score + additionalScore,
        hasAccused: false,
        hasVoted: false,
        role: null,
        specificRole: null,
      );
    }).toList();

    final isGameEnd = room.currentRound >= room.maxRounds;
    print('🎮 Is game end: $isGameEnd (round ${room.currentRound}/${room.maxRounds})');

    final updatedRoom = room.copyWith(
      players: updatedPlayers,
      state: isGameEnd ? GameState.gameEnd : GameState.roundEnd,
      currentLocation: null,
      spySeenLocations: [], // Tur bittiğinde casusun gördüğü mekanları sıfırla
      currentRound: isGameEnd ? room.currentRound : room.currentRound + 1,
      roundStartTime: null,
      roundEndTime: null,
      currentAccuserId: null,
      accusedPlayerId: null,
      votes: {},
      updatedAt: DateTime.now(),
    );

    print('💾 Updating room state to: ${updatedRoom.state}');
    await _database.ref('rooms/$roomId').update(updatedRoom.toJson());
    print('✅ Room updated successfully');

    // Analytics
    final gameDuration = room.roundStartTime != null
        ? DateTime.now().difference(room.roundStartTime!).inSeconds
        : 0;
    await AnalyticsService().logGameEnded(
      roomCode: room.code,
      spyWins: spyWins,
      reason: reason,
      roundNumber: room.currentRound,
      gameDurationSeconds: gameDuration,
    );
  }

  Future<void> resetGameToWaiting(String roomId) async {
    final roomRef = _database.ref('rooms/$roomId');
    final roomSnapshot = await roomRef.once();

    if (roomSnapshot.snapshot.value == null) {
      throw Exception('Room not found');
    }

    // Firebase'den gelen veriyi JSON string'e çevirip tekrar parse et
    final jsonString = jsonEncode(roomSnapshot.snapshot.value);
    final roomJson = jsonDecode(jsonString) as Map<String, dynamic>;
    final room = GameRoom.fromJson(roomJson);

    // Oyunu sıfırla ama skorları koru
    final resetPlayers = room.players.map((player) {
      return player.copyWith(
        role: null,
        specificRole: null,
        hasAccused: false,
        hasVoted: false,
        isReady: false,
        // score korunuyor
      );
    }).toList();

    final resetRoom = room.copyWith(
      players: resetPlayers,
      state: GameState.waiting,
      currentLocation: null,
      spySeenLocations: [], // Oyun sıfırlandığında casusun gördüğü mekanları da sıfırla
      currentRound: 1,
      roundStartTime: null,
      roundEndTime: null,
      currentAccuserId: null,
      accusedPlayerId: null,
      votes: {},
      updatedAt: DateTime.now(),
    );

    await roomRef.update(resetRoom.toJson());
  }

  Stream<GameRoom?> watchRoom(String roomId) {
    return _database.ref('rooms/$roomId').onValue
        .map((event) {
          if (event.snapshot.value == null) {
            return null;
          }

          try {
            // Firebase'den gelen veriyi JSON string'e çevirip tekrar parse et
            final rawData = event.snapshot.value;

            // JSON string'e çevir ve tekrar parse et
            final jsonString = jsonEncode(rawData);
            final roomJson = jsonDecode(jsonString) as Map<String, dynamic>;

            final room = GameRoom.fromJson(roomJson);

            // Debug: Print players' ready status from Firebase
            print('📡 Firebase data received for room $roomId:');
            for (final player in room.players) {
              print('   ${player.name}: isReady=${player.isReady}');
            }

            return room;
          } catch (e) {
            print('❌ Error parsing room data: $e');
            return null;
          }
        })
        .distinct((prev, next) {
          // Sadece önemli değişikliklerde stream'i güncelle
          if (prev == null || next == null) return false;

          return prev.id == next.id &&
                 prev.state == next.state &&
                 prev.players.length == next.players.length &&
                 prev.roundEndTime == next.roundEndTime &&
                 prev.currentAccuserId == next.currentAccuserId &&
                 prev.votes.length == next.votes.length;
        });
  }

  String _generateRoomCode() {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    final random = Random();
    return String.fromCharCodes(
      Iterable.generate(6, (_) => chars.codeUnitAt(random.nextInt(chars.length)))
    );
  }

}
